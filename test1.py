import cv2

def list_available_cameras(max_tries=20):
    """
    列出所有可用的摄像头设备及其编号
    
    参数:
        max_tries (int): 尝试检测的最大设备编号
        
    返回:
        list: 可用摄像头设备的编号列表
    """
    available_cameras = []
    
    for i in range(max_tries):
        cap = cv2.VideoCapture(i)
        if not cap.isOpened():
            cap.release()
            continue
        else:
            # 尝试读取一帧以确认设备是否真的可用
            ret, frame = cap.read()
            if ret:
                available_cameras.append(i)
            cap.release()
    
    return available_cameras

def open_camera(camera_id):
    """
    打开指定编号的摄像头并显示视频流
    
    参数:
        camera_id (int): 要打开的摄像头设备编号
    """
    cap = cv2.VideoCapture(camera_id)
    
    if not cap.isOpened():
        print(f"无法打开摄像头设备 {camera_id}")
        return
    
    print(f"成功打开摄像头设备 {camera_id}")
    print("按 'q' 键退出")
    
    while True:
        ret, frame = cap.read()
        
        if not ret:
            print("无法获取视频帧")
            break
        
        cv2.imshow(f"摄像头 {camera_id}", frame)
        
        # 按 'q' 键退出循环
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    # 释放资源
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    print("正在检测可用的摄像头设备...")
    cameras = list_available_cameras()
    
    if not cameras:
        print("未检测到可用的摄像头设备")
    else:
        print("检测到以下摄像头设备:")
        for camera_id in cameras:
            print(f"- 设备编号: {camera_id}")
        
        # 自动打开第一个可用的摄像头
        first_camera = cameras[0]
        print(f"\n正在自动打开第一个摄像头 (设备编号: {first_camera})")
        open_camera(first_camera)    