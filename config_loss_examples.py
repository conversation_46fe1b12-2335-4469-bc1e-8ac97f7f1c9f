"""
损失函数配置示例
展示如何配置不同类型的损失函数
"""
import os
import os.path as osp
import sys
import time
import numpy as np
from easydict import EasyDict as edict

# 基础配置模板
def get_base_config():
    C = edict()
    
    C.seed = 12345
    
    # 在Windows系统上使用cd命令代替pwd
    if os.name == 'nt':  # Windows系统
        remoteip = os.popen('cd').read()
    else:
        remoteip = os.popen('pwd').read()
    C.root_dir = os.path.abspath(os.path.join(os.getcwd(), './'))
    C.abs_dir = osp.realpath(".")
    
    # Dataset config
    C.dataset_name = 'PIE-RGB-SAR'
    C.dataset_path = osp.join(C.root_dir, 'datasets', 'PIE-RGB-SAR')
    C.rgb_root_folder = osp.join(C.dataset_path, 'RGB')
    C.rgb_format = '.tif'
    C.gt_root_folder = osp.join(C.dataset_path, 'Label')
    C.gt_format = '.tif'
    C.gt_transform = True
    C.x_root_folder = osp.join(C.dataset_path, 'SAR')
    C.x_format = '.tif'
    C.x_is_single_channel = True
    C.train_source = osp.join(C.dataset_path, "train.txt")
    C.eval_source = osp.join(C.dataset_path, "val.txt")
    C.is_test = False
    C.num_train_imgs = 2432
    C.num_eval_imgs = 2433
    C.num_classes = 6
    C.class_names = ['background', 'building', 'farmland', 'forest', 'water', 'road']
    
    # Image Config
    C.background = 255
    C.image_height = 256
    C.image_width = 256
    C.norm_mean = np.array([0.348, 0.370, 0.309])
    C.norm_std = np.array([0.197, 0.174, 0.171])
    
    # Network Settings
    C.backbone = 'swin_s'
    C.pretrained_model = osp.join(C.root_dir, 'swin_trans', 'swin_small_patch4_window7_224_22k.pth')
    C.decoder = 'UPernet'
    C.decoder_embed_dim = 512
    C.upernet_channels = 512
    C.optimizer = 'AdamW'
    
    # Train Config
    C.lr = 1e-5
    C.lr_power = 0.9
    C.momentum = 0.9
    C.weight_decay = 0.01
    C.grad_clip = 1.0
    C.batch_size = 8
    C.nepochs = 300
    C.niters_per_epoch = C.num_train_imgs // C.batch_size + 1
    C.num_workers = 0
    C.train_scale_array = [0.5, 0.75, 1, 1.25, 1.5, 1.75]
    C.warm_up_epoch = 20
    
    C.fix_bias = True
    C.bn_eps = 1e-3
    C.bn_momentum = 0.1
    
    # Eval Config
    C.eval_iter = 25
    C.eval_stride_rate = 2 / 3
    C.eval_scale_array = [1]
    C.eval_flip = False
    C.eval_crop_size = [256, 256]
    C.eval_batch_size = 12
    C.eval_frequency = 5
    
    # Store Config
    C.checkpoint_start_epoch = 1
    C.checkpoint_step = 5
    
    # Resume Config
    C.resume_from = None
    C.resume_model = None
    
    # Path Config
    def add_path(path):
        if path not in sys.path:
            sys.path.insert(0, path)
    add_path(osp.join(C.root_dir))
    
    C.log_dir = osp.abspath('log_' + C.dataset_name + '_' + C.backbone)
    C.tb_dir = osp.abspath(osp.join(C.log_dir, "tb"))
    C.log_dir_link = C.log_dir
    C.checkpoint_dir = osp.abspath(osp.join(C.log_dir, "checkpoint"))
    
    exp_time = time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime())
    C.log_file = C.log_dir + '/log_' + exp_time + '.log'
    C.link_log_file = C.log_file + '/log_last.log'
    C.val_log_file = C.log_dir + '/val_' + exp_time + '.log'
    C.link_val_log_file = C.log_dir + '/val_last.log'
    
    return C

# 配置示例1: 标准交叉熵损失
def get_ce_config():
    """标准交叉熵损失配置"""
    C = get_base_config()
    
    # 损失函数配置
    C.loss_type = 'ce'
    C.loss_weights = {
        'ce_weight': 1.0,
        'focal_weight': 0.0,
        'dice_weight': 0.0,
        'lovasz_weight': 0.0,
        'boundary_weight': 0.0,
        'context_weight': 0.0
    }
    C.label_smoothing = 0.1  # 添加标签平滑
    C.class_weights = None
    C.log_loss_components = False
    
    return C

# 配置示例2: Focal Loss（处理类别不平衡）
def get_focal_config():
    """Focal Loss配置，适用于类别不平衡"""
    C = get_base_config()
    
    # 损失函数配置
    C.loss_type = 'focal'
    C.focal_gamma = 2.0
    C.focal_alpha = [1.0, 2.0, 1.5, 1.0, 1.0, 1.0]  # 对应6个类别的权重
    C.label_smoothing = 0.0
    C.class_weights = None
    C.log_loss_components = True
    
    return C

# 配置示例3: 简化组合损失（CE + Focal）
def get_simplified_combined_config():
    """简化组合损失配置"""
    C = get_base_config()
    
    # 损失函数配置
    C.loss_type = 'simplified_combined'
    C.loss_weights = {
        'ce_weight': 0.7,
        'focal_weight': 0.3,
        'dice_weight': 0.0,
        'lovasz_weight': 0.0,
        'boundary_weight': 0.0,
        'context_weight': 0.0
    }
    C.focal_gamma = 2.0
    C.focal_alpha = None
    C.label_smoothing = 0.05
    C.class_weights = None
    C.log_loss_components = True
    
    return C

# 配置示例4: 完整组合损失
def get_combined_config():
    """完整组合损失配置"""
    C = get_base_config()
    
    # 损失函数配置
    C.loss_type = 'combined'
    C.loss_weights = {
        'ce_weight': 0.4,
        'focal_weight': 0.3,
        'dice_weight': 0.2,
        'lovasz_weight': 0.1,
        'boundary_weight': 0.0,
        'context_weight': 0.0
    }
    C.focal_gamma = 2.0
    C.focal_alpha = None
    C.label_smoothing = 0.0
    C.class_weights = None
    C.log_loss_components = True
    
    return C

# 配置示例5: 边界敏感配置
def get_boundary_sensitive_config():
    """边界敏感损失配置"""
    C = get_base_config()
    
    # 损失函数配置
    C.loss_type = 'combined'
    C.loss_weights = {
        'ce_weight': 0.5,
        'focal_weight': 0.2,
        'dice_weight': 0.2,
        'lovasz_weight': 0.0,
        'boundary_weight': 0.1,
        'context_weight': 0.0
    }
    C.focal_gamma = 2.0
    C.focal_alpha = None
    C.label_smoothing = 0.0
    C.class_weights = None
    C.context_kernel_size = 3
    C.context_dilation = 1
    C.log_loss_components = True
    
    return C

# 配置示例6: 类别权重配置
def get_class_weighted_config():
    """使用类别权重的配置"""
    C = get_base_config()
    
    # 损失函数配置
    C.loss_type = 'ce'
    C.loss_weights = {
        'ce_weight': 1.0,
        'focal_weight': 0.0,
        'dice_weight': 0.0,
        'lovasz_weight': 0.0,
        'boundary_weight': 0.0,
        'context_weight': 0.0
    }
    C.label_smoothing = 0.0
    # 根据类别频率设置权重：背景、建筑物、农田、森林、水域、道路
    C.class_weights = [0.5, 2.0, 1.5, 2.5, 3.0, 2.0]
    C.log_loss_components = True
    
    return C

# 使用示例
if __name__ == '__main__':
    print("损失函数配置示例:")
    print("1. 标准交叉熵损失")
    print("2. Focal Loss（类别不平衡）")
    print("3. 简化组合损失（CE + Focal）")
    print("4. 完整组合损失")
    print("5. 边界敏感配置")
    print("6. 类别权重配置")
    
    # 示例：获取简化组合损失配置
    config = get_simplified_combined_config()
    print(f"\n简化组合损失配置:")
    print(f"损失类型: {config.loss_type}")
    print(f"损失权重: {config.loss_weights}")
    print(f"Focal gamma: {config.focal_gamma}")
    print(f"标签平滑: {config.label_smoothing}")
