{"python.defaultInterpreterPath": "D:\\pycharm exercise\\CMX_main\\.venv\\Scripts\\python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "terminal.integrated.env.windows": {"PATH": "D:\\pycharm exercise\\CMX_main\\.venv\\Scripts;${env:PATH}"}, "python.envFile": "${workspaceFolder}/.env", "python.autoComplete.extraPaths": ["${workspaceFolder}"], "python.analysis.extraPaths": ["${workspaceFolder}"], "terminal.integrated.defaultProfile.windows": "PowerShell with Venv", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "args": ["-ExecutionPolicy", "Bypass"]}, "PowerShell with Venv": {"source": "PowerShell", "args": ["-ExecutionPolicy", "Bypass", "-NoExit", "-Command", "& 'D:\\pycharm exercise\\CMX_main\\.venv\\Scripts\\Activate.ps1'; Write-Host '虚拟环境已激活' -ForegroundColor Green"]}}}