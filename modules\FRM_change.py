import torch
import torch.nn as nn
from timm.models.layers import trunc_normal_
import math
import torch.nn.functional as F
from .MSFblock import MSFblock


class AdaptiveFeatureFusion(nn.Module):
    """自适应特征融合 - 基于特征相似性动态调整融合权重"""
    def __init__(self, dim, reduction=16):
        super().__init__()
        self.dim = dim

        # 特征相似性计算
        self.similarity_conv = nn.Sequential(
            nn.Conv2d(dim * 2, dim // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim // reduction, 1, 1),
            nn.Sigmoid()
        )

        # 自适应权重生成
        self.weight_gen = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(dim * 2, dim // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim // reduction, 2, 1),
            nn.Softmax(dim=1)
        )

        # 特征增强
        self.enhance_conv = nn.Sequential(
            nn.Conv2d(dim, dim, 3, padding=1, groups=dim),  # 深度可分离卷积
            nn.Conv2d(dim, dim, 1),
            nn.ReLU(inplace=True)
        )

    def forward(self, x1, x2):
        B, C, H, W = x1.shape

        # 计算特征相似性
        concat_feat = torch.cat([x1, x2], dim=1)
        similarity = self.similarity_conv(concat_feat)  # B, 1, H, W，这个是注意力分数，经过了sigmoid的

        # 生成自适应权重
        global_weights = self.weight_gen(concat_feat)  # B, 2, 1, 1
        w1, w2 = global_weights[:, 0:1], global_weights[:, 1:2]

        # 在相似区域减少权重（避免冗余），在差异区域增强权重（利用互补性）
        complement_factor = 1 - similarity  # 差异程度
        local_w1 = w1 * (0.5 + 0.5 * complement_factor)  # 差异越大，权重越高
        local_w2 = w2 * (0.5 + 0.5 * complement_factor)  # 两个模态都在差异区域增强

        out1 = x1 + local_w1 * x1
        out2 = x2 + local_w2 * x2

        return out1, out2
    

# 特征校正模块
class ChannelWeights(nn.Module):
    def __init__(self, dim, reduction=1):
        super(ChannelWeights, self).__init__()
        self.dim = dim
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.mlp = nn.Sequential(
            nn.Linear(self.dim * 6, self.dim * 6 // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(self.dim * 6 // reduction, self.dim * 2),
            nn.Sigmoid())

    def forward(self, x1, x2):
        B, _, H, W = x1.shape
        x = torch.cat((x1, x2), dim=1)
        avg = self.avg_pool(x).view(B, self.dim * 2)    # (B,2C)
        std = torch.std(x, dim=(2, 3), keepdim=True).view(B, self.dim * 2)
        max = self.max_pool(x).view(B, self.dim * 2)
        y = torch.cat((avg, std, max), dim=1)  # B 6C
        y = self.mlp(y).view(B, self.dim * 2, 1)
        channel_weights = y.reshape(B, 2, self.dim, 1, 1).permute(1, 0, 2, 3, 4)  # 2 B C 1 1
        
        return channel_weights


class SpatialWeights(nn.Module):
    def __init__(self, dim, reduction=1):
        super(SpatialWeights, self).__init__()
        self.dim = dim
        self.mlp = nn.Sequential(
            nn.Conv2d(self.dim * 2, self.dim // reduction, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.dim // reduction, 2, kernel_size=1),
            nn.Sigmoid())

    def forward(self, x1, x2):
        B, _, H, W = x1.shape
        x = torch.cat((x1, x2), dim=1)  # B 2C H W
        spatial_weights = self.mlp(x).reshape(B, 2, 1, H, W).permute(1, 0, 2, 3, 4)  # 2 B 1 H W

        return spatial_weights


# 先空间校正再通道校正
class FeatureCorrection_s2c(nn.Module):
    def __init__(self, dim, reduction=1, eps=1e-8):
        super(FeatureCorrection_s2c, self).__init__()
        # 自定义可训练权重参数
        self.weights1 = nn.Parameter(torch.ones(2, dtype=torch.float32), requires_grad=True)
        self.eps = eps
        self.AFF = AdaptiveFeatureFusion(dim=dim)
        self.spatial_weights = SpatialWeights(dim=dim, reduction=reduction)
        # 使用MSFblock替换ChannelWeights
        self.msf_block = MSFblock(in_channels=dim)

        self.apply(self._init_weights)

    @classmethod
    def _init_weights(cls, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x1, x2):
        # 对可训练权重 self.weights 应用 ReLU 激活函数，确保权重非负
        weights1 = nn.ReLU()(self.weights1)
        # 对经过 ReLU 处理后的权重进行归一化，使得两个权重之和为 1，self.eps 是一个极小值，用于避免分母为零
        fuse_weights1 = weights1 / (torch.sum(weights1, dim=0) + self.eps)
        
        x1_1, x2_1 = self.AFF(x1, x2)
        
        # 使用MSFblock生成融合的通道注意力特征，接收空间校正后的 x1_1 和 x2_1 作为输入
        channel_fused = self.msf_block(x1_1, x2_1)

        # 使用融合的通道注意力特征进行通道校正
        # 将融合特征与原始特征结合，替换原来的通道权重机制
        main_out = x1_1 + channel_fused
        aux_out = x2_1 + channel_fused

        # 返回校正后的两个特征图，形状为(B,C,H,W)
        return main_out, aux_out


# ============== 更先进的特征融合方法 ==============

class CrossModalAttention(nn.Module):
    """跨模态注意力机制 - 让两个模态特征相互关注重要区域"""
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.q_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.k_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.v_proj = nn.Linear(dim, dim, bias=qkv_bias)

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x1, x2):
        B, C, H, W = x1.shape
        # 将特征图展平为序列
        x1_flat = x1.flatten(2).transpose(1, 2)  # B, HW, C
        x2_flat = x2.flatten(2).transpose(1, 2)  # B, HW, C

        # x1作为query，x2作为key和value
        q = self.q_proj(x1_flat).reshape(B, H*W, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)
        k = self.k_proj(x2_flat).reshape(B, H*W, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)
        v = self.v_proj(x2_flat).reshape(B, H*W, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, H*W, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        return x.transpose(1, 2).reshape(B, C, H, W)


class GatedFusion(nn.Module):
    """门控融合机制 - 学习何时使用哪个模态的信息"""
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

        # 门控网络
        self.gate_conv = nn.Sequential(
            nn.Conv2d(dim * 2, dim, 3, padding=1),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim, dim, 3, padding=1),
            nn.BatchNorm2d(dim),
            nn.Sigmoid()
        )

        # 特征变换
        self.transform1 = nn.Sequential(
            nn.Conv2d(dim, dim, 3, padding=1),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=True)
        )

        self.transform2 = nn.Sequential(
            nn.Conv2d(dim, dim, 3, padding=1),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x1, x2):
        # 生成门控信号
        concat_feat = torch.cat([x1, x2], dim=1)
        gate = self.gate_conv(concat_feat)  # B, C, H, W，这个是门控分数

        # 特征变换
        x1_trans = self.transform1(x1)
        x2_trans = self.transform2(x2)

        # 门控融合
        fused = gate * x1_trans + (1 - gate) * x2_trans

        return fused


class ComplementaryFusion(nn.Module):
    """互补性特征融合 - 专门设计用于增强双模态特征交互"""
    def __init__(self, dim, fusion_strategy='complement_enhance'):
        super().__init__()
        self.dim = dim
        self.fusion_strategy = fusion_strategy

        # 特征差异性检测
        self.difference_conv = nn.Sequential(
            nn.Conv2d(dim * 2, dim // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim // 4, 1, 1),
            nn.Sigmoid()
        )

        # 互补性权重生成
        self.complement_weight = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(dim * 2, dim // 8, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim // 8, 2, 1),
            nn.Sigmoid()
        )

        # 交互增强模块
        self.interaction_conv = nn.Sequential(
            nn.Conv2d(dim * 2, dim, 3, padding=1),
            nn.BatchNorm2d(dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim, dim, 3, padding=1),
            nn.BatchNorm2d(dim)
        )

    def forward(self, x1, x2):
        concat_feat = torch.cat([x1, x2], dim=1)

        # 计算特征差异性（而非相似性）
        difference = self.difference_conv(concat_feat)  # B, 1, H, W

        # 生成互补权重
        complement_weights = self.complement_weight(concat_feat)  # B, 2, 1, 1
        w1, w2 = complement_weights[:, 0:1], complement_weights[:, 1:2]

        if self.fusion_strategy == 'complement_enhance':
            # 策略1：在差异区域增强两个模态的交互
            enhance_factor = difference  # 差异越大，增强越多
            local_w1 = w1 * (0.5 + 0.5 * enhance_factor)
            local_w2 = w2 * (0.5 + 0.5 * enhance_factor)

        elif self.fusion_strategy == 'selective_complement':
            # 策略2：选择性互补 - 在差异区域选择更强的模态
            local_w1 = w1 * difference + w1 * 0.3 * (1 - difference)
            local_w2 = w2 * difference + w2 * 0.3 * (1 - difference)

        elif self.fusion_strategy == 'cross_enhance':
            # 策略3：交叉增强 - 用一个模态的差异区域增强另一个模态
            local_w1 = w1 * (1 + 0.5 * difference)  # x1在差异区域获得更多权重
            local_w2 = w2 * (1 + 0.5 * (1 - difference))  # x2在相似区域获得更多权重

        else:  # 'balanced'
            # 策略4：平衡策略 - 保持权重平衡但增强交互
            local_w1 = w1
            local_w2 = w2

        # 应用权重
        weighted_x1 = local_w1 * x1
        weighted_x2 = local_w2 * x2

        # 交互增强
        interaction_feat = self.interaction_conv(torch.cat([weighted_x1, weighted_x2], dim=1))

        # 最终融合
        fused = weighted_x1 + weighted_x2 + interaction_feat

        return fused


class AdvancedFeatureCorrection(nn.Module):
    """改进的特征校正模块 - 集成多种先进融合策略"""
    def __init__(self, dim, fusion_type='adaptive', reduction=1, eps=1e-8):
        super().__init__()
        self.fusion_type = fusion_type
        self.eps = eps

        # 可训练权重参数
        self.weights = nn.Parameter(torch.ones(2, dtype=torch.float32), requires_grad=True)

        # 空间权重模块
        self.spatial_weights = SpatialWeights(dim=dim, reduction=reduction)

        # 选择融合策略
        if fusion_type == 'cross_attention':
            self.fusion_module = CrossModalAttention(dim)
        elif fusion_type == 'adaptive':
            self.fusion_module = AdaptiveFeatureFusion(dim, reduction)
        elif fusion_type == 'gated':
            self.fusion_module = GatedFusion(dim)
        else:  # 默认使用MSFblock
            self.fusion_module = MSFblock(dim)

        # 残差连接的投影层
        self.residual_proj = nn.Sequential(
            nn.Conv2d(dim, dim, 1),
            nn.BatchNorm2d(dim)
        )

        self.apply(self._init_weights)

    @classmethod
    def _init_weights(cls, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x1, x2):
        # 归一化权重
        weights = F.relu(self.weights)
        fuse_weights = weights / (torch.sum(weights, dim=0) + self.eps)

        # 空间注意力
        spatial_weights = self.spatial_weights(x1, x2)

        # 改进的空间校正 - 使用更复杂的交互
        x1_enhanced = x1 + fuse_weights[0] * spatial_weights[1] * x2
        x2_enhanced = x2 + fuse_weights[1] * spatial_weights[0] * x1

        # 应用选择的融合策略
        if self.fusion_type == 'cross_attention':
            # 跨模态注意力融合
            x1_attended = self.fusion_module(x1_enhanced, x2_enhanced)
            x2_attended = self.fusion_module(x2_enhanced, x1_enhanced)

            # 残差连接
            main_out = x1_enhanced + self.residual_proj(x1_attended)
            aux_out = x2_enhanced + self.residual_proj(x2_attended)

        elif self.fusion_type in ['adaptive', 'gated']:
            # 自适应或门控融合
            fused_feature = self.fusion_module(x1_enhanced, x2_enhanced)

            # 分别与原始特征结合
            main_out = x1_enhanced + self.residual_proj(fused_feature)
            aux_out = x2_enhanced + self.residual_proj(fused_feature)

        else:  # MSFblock
            channel_fused = self.fusion_module(x1_enhanced, x2_enhanced)
            main_out = x1_enhanced + channel_fused
            aux_out = x2_enhanced + channel_fused

        return main_out, aux_out
