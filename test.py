import os
import os.path as osp
import sys
import time
import argparse
import numpy as np
from tqdm import tqdm
import cv2
import matplotlib.pyplot as plt
from tabulate import tabulate
import pandas as pd

import torch
import torch.nn as nn
import torch.backends.cudnn as cudnn

from config import config
from models.builder import EncoderDecoder as segmodel
from dataloader.RGBXDataset import RGBXDataset
from dataloader.dataloader import ValPre
from utils.metric import hist_info, compute_score
from utils.pyt_utils import ensure_dir, load_model
from utils.loss_manager import LossManager
from engine.logger import get_logger
from engine.engine import Engine

logger = get_logger()

class Tester:
    def __init__(self, model_path, output_dir=None, devices=None):
        self.model_path = model_path
        self.devices = devices if devices else [0]
        
        # 设置输出目录
        if output_dir:
            self.output_dir = output_dir
        else:
            self.output_dir = os.path.join(config.root_dir, 'test_results')
        ensure_dir(self.output_dir)
        
        # 创建可视化结果目录
        self.vis_dir = os.path.join(self.output_dir, 'visualizations')
        ensure_dir(self.vis_dir)
        
        # 设置设备
        self.device = torch.device(f"cuda:{self.devices[0]}" if torch.cuda.is_available() else "cpu")
        
        # 创建模型
        self.model = self._build_model()
        
        # 创建测试数据集
        self.dataset = self._build_dataset()
        
        # 定义类别颜色
        self.colors = [
            [128, 128, 128],  # 背景 - 灰色
            [255, 0, 0],      # 建筑物 - 红色
            [0, 255, 0],      # 农田 - 绿色
            [0, 0, 255],      # 森林 - 蓝色
            [255, 255, 0],    # 水域 - 黄色
            [255, 0, 255]     # 道路 - 紫色
        ]
    
    def _build_model(self):
        """构建并加载模型"""
        logger.info("构建模型...")
        
        # 创建模型
        BatchNorm2d = nn.BatchNorm2d
        # 使用新的损失函数管理器
        loss_manager = LossManager(config)
        model = segmodel(cfg=config, criterion=loss_manager, norm_layer=BatchNorm2d)
        
        # 加载模型权重
        logger.info(f"加载模型权重: {self.model_path}")
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            if 'model' in checkpoint:
                model = load_model(model, checkpoint['model'])
            else:
                model = load_model(model, checkpoint)
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
        
        # 将模型移至GPU
        model = model.to(self.device)
        model.eval()
        
        return model
    
    def _build_dataset(self):
        """创建测试数据集"""
        logger.info("创建测试数据集...")
        
        data_setting = {
            'rgb_root': config.rgb_root_folder,
            'rgb_format': config.rgb_format,
            'gt_root': config.gt_root_folder,
            'gt_format': config.gt_format,
            'transform_gt': config.gt_transform,
            'x_root': config.x_root_folder,
            'x_format': config.x_format,
            'x_single_channel': config.x_is_single_channel,
            'class_names': config.class_names,
            'train_source': config.train_source,
            'eval_source': config.eval_source,
            'class_names': config.class_names
        }
        
        val_pre = ValPre()
        dataset = RGBXDataset(data_setting, 'val', val_pre)
        logger.info(f"测试数据集创建成功，共 {len(dataset)} 张图像")
        
        return dataset
    
    def test(self):
        """测试模型并计算指标"""
        logger.info("开始测试...")
        
        # 初始化指标
        hist = np.zeros((config.num_classes, config.num_classes))
        correct = 0
        labeled = 0
        
        # 选择20张图片用于可视化
        test_indices = list(range(len(self.dataset)))
        vis_indices = test_indices[:min(20, len(test_indices))]
        
        # 存储所有结果
        results = []
        
        with torch.no_grad():
            for idx in tqdm(range(len(self.dataset))):
                try:
                    data = self.dataset[idx]
                    img = data['data']
                    label = data['label']
                    modal_x = data['modal_x']
                    name = data['fn']
                    
                    # 预测
                    pred = self.sliding_eval_rgbX(img, modal_x)
                    
                    # 计算指标
                    hist_tmp, labeled_tmp, correct_tmp = hist_info(config.num_classes, pred, label)
                    hist += hist_tmp
                    correct += correct_tmp
                    labeled += labeled_tmp
                    
                    # 如果是选中的图片，保存可视化结果
                    if idx in vis_indices:
                        self.save_visualization(img, modal_x, label, pred, name)
                        results.append((img, modal_x, label, pred, name))
                except Exception as e:
                    logger.error(f"处理测试样本 {idx} 时出错: {e}")
                    continue
        
        # 计算总体指标
        iou, mean_IoU, _, freq_IoU, mean_pixel_acc, pixel_acc = compute_score(hist, correct, labeled)
        
        # 计算每个类别的precision和recall
        precision = np.zeros(config.num_classes)
        recall = np.zeros(config.num_classes)
        
        for i in range(config.num_classes):
            # Precision = TP / (TP + FP)
            precision[i] = hist[i, i] / (hist[:, i].sum() + 1e-10)
            # Recall = TP / (TP + FN)
            recall[i] = hist[i, i] / (hist[i, :].sum() + 1e-10)
        
        # 打印结果表格
        self.print_results_table(iou, precision, recall, np.diag(hist) / (hist.sum(axis=1) + 1e-10))
        
        # 保存结果到CSV
        self.save_results_to_csv(iou, precision, recall, np.diag(hist) / (hist.sum(axis=1) + 1e-10))
        
        logger.info(f"测试完成，结果已保存到 {self.output_dir}")
        
        return mean_IoU
    
    def sliding_eval_rgbX(self, img, modal_x):
        """滑动窗口评估"""
        ori_rows, ori_cols, _ = img.shape
        processed_pred = np.zeros((ori_rows, ori_cols, config.num_classes))
        
        # 处理图像
        input_data, input_modal_x = self.process_image_rgbX(img, modal_x)
        
        input_data = np.ascontiguousarray(input_data[None, :, :, :], dtype=np.float32)
        input_data = torch.FloatTensor(input_data).to(self.device)
        
        input_modal_x = np.ascontiguousarray(input_modal_x[None, :, :, :], dtype=np.float32)
        input_modal_x = torch.FloatTensor(input_modal_x).to(self.device)
        
        with torch.no_grad():
            score = self.model(input_data, input_modal_x)
            score = score[0]
        
        score = score.permute(1, 2, 0)
        data_output = cv2.resize(score.cpu().numpy(), (ori_cols, ori_rows), interpolation=cv2.INTER_LINEAR)
        processed_pred += data_output
        
        pred = processed_pred.argmax(2)
        return pred
    
    def process_image_rgbX(self, img, modal_x):
        """处理图像和模态X"""
        p_img = img
        p_modal_x = modal_x
        
        # 标准化
        p_img = self.normalize(p_img, config.norm_mean, config.norm_std)
        if len(modal_x.shape) == 2:
            p_modal_x = self.normalize(p_modal_x, 0, 1)
            p_modal_x = p_modal_x[np.newaxis, ...]
        else:
            p_modal_x = self.normalize(p_modal_x, config.norm_mean, config.norm_std)
            p_modal_x = p_modal_x.transpose(2, 0, 1)
        
        p_img = p_img.transpose(2, 0, 1)
        
        return p_img, p_modal_x
    
    def normalize(self, img, mean, std):
        """标准化图像"""
        if len(img.shape) == 3:
            img = img.astype(np.float32) / 255.0
            img -= np.array(mean)
            img /= np.array(std)
        else:
            img = img.astype(np.float32) / 255.0
            img -= mean
            img /= std
        return img
    
    def save_visualization(self, img, modal_x, label, pred, name):
        """保存可视化结果"""
        # 创建可视化图像
        plt.figure(figsize=(15, 10))
        
        # 原始RGB图像
        plt.subplot(2, 3, 1)
        plt.imshow(img)
        plt.title('RGB Image')
        plt.axis('off')
        
        # 原始SAR图像
        plt.subplot(2, 3, 2)
        if len(modal_x.shape) == 2:
            plt.imshow(modal_x, cmap='gray')
        else:
            plt.imshow(modal_x)
        plt.title('SAR Image')
        plt.axis('off')
        
        # 真实标签
        plt.subplot(2, 3, 3)
        # 确保label是2D数组
        if len(label.shape) == 3:
            if label.shape[2] == 1:
                label = label[:, :, 0]
            else:
                label = label[:, :, 0]
        elif len(label.shape) > 2:
            label = label.reshape(label.shape[0], label.shape[1])

        label_vis = np.zeros((label.shape[0], label.shape[1], 3), dtype=np.uint8)
        for i in range(config.num_classes):
            mask = (label == i)
            if np.any(mask):
                # 逐通道赋值，避免广播错误
                label_vis[mask, 0] = self.colors[i][0]
                label_vis[mask, 1] = self.colors[i][1]
                label_vis[mask, 2] = self.colors[i][2]
        plt.imshow(label_vis)
        plt.title('Ground Truth')
        plt.axis('off')

        # 预测结果
        plt.subplot(2, 3, 4)
        # 确保pred是2D数组
        if len(pred.shape) == 3:
            if pred.shape[2] == 1:
                pred = pred[:, :, 0]
            else:
                pred = pred[:, :, 0]
        elif len(pred.shape) > 2:
            pred = pred.reshape(pred.shape[0], pred.shape[1])

        pred_vis = np.zeros((pred.shape[0], pred.shape[1], 3), dtype=np.uint8)
        for i in range(config.num_classes):
            mask = (pred == i)
            if np.any(mask):
                # 逐通道赋值，避免广播错误
                pred_vis[mask, 0] = self.colors[i][0]
                pred_vis[mask, 1] = self.colors[i][1]
                pred_vis[mask, 2] = self.colors[i][2]
        plt.imshow(pred_vis)
        plt.title('Prediction')
        plt.axis('off')
        
        # RGB+预测融合图
        plt.subplot(2, 3, 5)
        # 创建半透明的预测掩膜
        alpha = 0.5
        rgb_pred = img.copy()
        for i in range(config.num_classes):
            mask = pred == i
            rgb_pred[mask] = rgb_pred[mask] * (1 - alpha) + np.array(self.colors[i]) * alpha
        plt.imshow(rgb_pred)
        plt.title('RGB + Prediction')
        plt.axis('off')
        
        # SAR+预测融合图
        plt.subplot(2, 3, 6)
        # 创建半透明的预测掩膜
        sar_pred = modal_x.copy()
        if len(sar_pred.shape) == 2:
            sar_pred = cv2.cvtColor(sar_pred, cv2.COLOR_GRAY2RGB)
        for i in range(config.num_classes):
            mask = pred == i
            sar_pred[mask] = sar_pred[mask] * (1 - alpha) + np.array(self.colors[i]) * alpha
        plt.imshow(sar_pred)
        plt.title('SAR + Prediction')
        plt.axis('off')
        
        # 保存图像
        plt.tight_layout()
        plt.savefig(os.path.join(self.vis_dir, f'{name}.png'), dpi=200)
        plt.close()
    
    def print_results_table(self, iou, precision, recall, accuracy):
        """打印结果表格"""
        headers = ["Class", "IoU", "Precision", "Recall", "Accuracy"]
        table_data = []
        
        for i in range(config.num_classes):
            table_data.append([
                config.class_names[i],
                f"{iou[i]:.4f}",
                f"{precision[i]:.4f}",
                f"{recall[i]:.4f}",
                f"{accuracy[i]:.4f}"
            ])
        
        # 添加平均值
        table_data.append([
            "Mean",
            f"{np.nanmean(iou):.4f}",
            f"{np.nanmean(precision):.4f}",
            f"{np.nanmean(recall):.4f}",
            f"{np.nanmean(accuracy):.4f}"
        ])
        
        # 打印表格
        table = tabulate(table_data, headers=headers, tablefmt="grid")
        logger.info(f"测试结果:\n{table}")
    
    def save_results_to_csv(self, iou, precision, recall, accuracy):
        """保存结果到CSV文件"""
        results = []
        
        for i in range(config.num_classes):
            results.append({
                "Class": config.class_names[i],
                "IoU": iou[i],
                "Precision": precision[i],
                "Recall": recall[i],
                "Accuracy": accuracy[i]
            })
        
        # 添加平均值
        results.append({
            "Class": "Mean",
            "IoU": np.nanmean(iou),
            "Precision": np.nanmean(precision),
            "Recall": np.nanmean(recall),
            "Accuracy": np.nanmean(accuracy)
        })
        
        # 保存到CSV
        df = pd.DataFrame(results)
        csv_path = os.path.join(self.output_dir, "test_results.csv")
        df.to_csv(csv_path, index=False)
        logger.info(f"结果已保存到: {csv_path}")

def main():
    parser = argparse.ArgumentParser(description='测试多模态语义分割模型')
    
    parser.add_argument('-m', '--model', type=str, required=True, default='log_PIE-RGB-SAR_mit_b2/checkpoint/best_model.pth',
                      help='模型权重路径，例如 "log_PIE-RGB-SAR_mit_b2/checkpoint/best_model.pth"')
    parser.add_argument('-d', '--devices', default='0', type=str,
                      help='指定使用的GPU设备，例如 "0" 或 "0,1,2,3"')
    parser.add_argument('-o', '--output', type=str, default=None,
                      help='输出目录，默认为 "test_results"')
    
    args = parser.parse_args()
    
    # 解析设备
    if args.devices:
        os.environ["CUDA_VISIBLE_DEVICES"] = args.devices
        devices = [int(x) for x in args.devices.split(',')]
    else:
        devices = [0]
    
    # 设置随机种子
    torch.manual_seed(config.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(config.seed)
    
    # 创建测试器并运行测试
    tester = Tester(args.model, args.output, devices)
    mean_iou = tester.test()
    
    logger.info(f"测试完成，平均IoU: {mean_iou:.4f}")

if __name__ == '__main__':
    main()
