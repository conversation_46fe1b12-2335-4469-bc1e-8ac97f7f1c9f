#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化不同特征融合策略的效果
帮助理解各种权重调制方法的作用机制
"""

import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle

def create_synthetic_features():
    """创建合成的双模态特征用于演示"""
    # 创建64x64的特征图
    size = 64
    
    # RGB特征：左半部分有强响应，右半部分较弱
    rgb_feat = np.zeros((size, size))
    rgb_feat[:, :size//2] = 0.8  # 左半部分强
    rgb_feat[:, size//2:] = 0.3  # 右半部分弱
    
    # SAR特征：右半部分有强响应，左半部分较弱  
    sar_feat = np.zeros((size, size))
    sar_feat[:, :size//2] = 0.2  # 左半部分弱
    sar_feat[:, size//2:] = 0.9  # 右半部分强
    
    # 添加一些噪声使其更真实
    rgb_feat += np.random.normal(0, 0.05, (size, size))
    sar_feat += np.random.normal(0, 0.05, (size, size))
    
    # 限制在[0,1]范围
    rgb_feat = np.clip(rgb_feat, 0, 1)
    sar_feat = np.clip(sar_feat, 0, 1)
    
    return rgb_feat, sar_feat

def compute_similarity(feat1, feat2):
    """计算特征相似性"""
    # 简单的相似性计算：1 - |feat1 - feat2|
    diff = np.abs(feat1 - feat2)
    similarity = 1 - diff
    return similarity

def visualize_fusion_strategies():
    """可视化不同的融合策略"""
    
    # 创建合成特征
    rgb_feat, sar_feat = create_synthetic_features()
    similarity = compute_similarity(rgb_feat, sar_feat)
    difference = 1 - similarity
    
    # 全局权重（假设）
    w1, w2 = 0.6, 0.4
    
    # 计算不同策略的权重
    strategies = {
        '原始策略': {
            'w1': w1 * similarity,
            'w2': w2 * (1 - similarity),
            'description': '相似区域偏向RGB，差异区域偏向SAR'
        },
        '互补增强': {
            'w1': w1 * (0.5 + 0.5 * difference),
            'w2': w2 * (0.5 + 0.5 * difference), 
            'description': '差异区域增强两个模态'
        },
        '选择性互补': {
            'w1': w1 * difference + w1 * 0.3 * (1 - difference),
            'w2': w2 * difference + w2 * 0.3 * (1 - difference),
            'description': '差异区域主导，相似区域保留30%'
        },
        '交叉增强': {
            'w1': w1 * (1 + 0.5 * difference),
            'w2': w2 * (1 + 0.5 * similarity),
            'description': 'RGB在差异区域增强，SAR在相似区域增强'
        }
    }
    
    # 创建可视化
    fig, axes = plt.subplots(3, 5, figsize=(20, 12))
    fig.suptitle('双模态特征融合策略对比', fontsize=16, fontweight='bold')
    
    # 第一行：输入特征和相似性
    im1 = axes[0, 0].imshow(rgb_feat, cmap='Reds', vmin=0, vmax=1)
    axes[0, 0].set_title('RGB特征\n(左强右弱)', fontweight='bold')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0], fraction=0.046)
    
    im2 = axes[0, 1].imshow(sar_feat, cmap='Blues', vmin=0, vmax=1)
    axes[0, 1].set_title('SAR特征\n(左弱右强)', fontweight='bold')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1], fraction=0.046)
    
    im3 = axes[0, 2].imshow(similarity, cmap='Greens', vmin=0, vmax=1)
    axes[0, 2].set_title('特征相似性\n(绿色=相似)', fontweight='bold')
    axes[0, 2].axis('off')
    plt.colorbar(im3, ax=axes[0, 2], fraction=0.046)
    
    im4 = axes[0, 3].imshow(difference, cmap='Oranges', vmin=0, vmax=1)
    axes[0, 3].set_title('特征差异性\n(橙色=差异大)', fontweight='bold')
    axes[0, 3].axis('off')
    plt.colorbar(im4, ax=axes[0, 3], fraction=0.046)
    
    # 添加说明
    axes[0, 4].text(0.1, 0.8, '特征分析:', fontweight='bold', fontsize=12, transform=axes[0, 4].transAxes)
    axes[0, 4].text(0.1, 0.7, '• 左侧：RGB强，SAR弱', fontsize=10, transform=axes[0, 4].transAxes)
    axes[0, 4].text(0.1, 0.6, '• 右侧：RGB弱，SAR强', fontsize=10, transform=axes[0, 4].transAxes)
    axes[0, 4].text(0.1, 0.5, '• 中间：过渡区域', fontsize=10, transform=axes[0, 4].transAxes)
    axes[0, 4].text(0.1, 0.3, '理想融合策略:', fontweight='bold', fontsize=12, transform=axes[0, 4].transAxes)
    axes[0, 4].text(0.1, 0.2, '• 左侧多用RGB', fontsize=10, transform=axes[0, 4].transAxes)
    axes[0, 4].text(0.1, 0.1, '• 右侧多用SAR', fontsize=10, transform=axes[0, 4].transAxes)
    axes[0, 4].axis('off')
    
    # 第二行和第三行：不同策略的权重
    strategy_names = list(strategies.keys())
    for i, (name, strategy) in enumerate(strategies.items()):
        # RGB权重
        im_w1 = axes[1, i].imshow(strategy['w1'], cmap='Reds', vmin=0, vmax=1)
        axes[1, i].set_title(f'{name}\nRGB权重', fontweight='bold')
        axes[1, i].axis('off')
        plt.colorbar(im_w1, ax=axes[1, i], fraction=0.046)
        
        # SAR权重  
        im_w2 = axes[2, i].imshow(strategy['w2'], cmap='Blues', vmin=0, vmax=1)
        axes[2, i].set_title(f'SAR权重\n{strategy["description"]}', fontweight='bold')
        axes[2, i].axis('off')
        plt.colorbar(im_w2, ax=axes[2, i], fraction=0.046)
    
    # 第二行第5列：权重分析
    axes[1, 4].text(0.1, 0.9, '权重分析:', fontweight='bold', fontsize=12, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.8, '红色越深 = RGB权重越高', fontsize=10, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.7, '蓝色越深 = SAR权重越高', fontsize=10, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.5, '策略对比:', fontweight='bold', fontsize=12, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.4, '• 原始：可能浪费互补信息', fontsize=9, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.3, '• 互补增强：充分利用差异', fontsize=9, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.2, '• 选择性：平衡利用', fontsize=9, transform=axes[1, 4].transAxes)
    axes[1, 4].text(0.1, 0.1, '• 交叉：错位增强', fontsize=9, transform=axes[1, 4].transAxes)
    axes[1, 4].axis('off')
    
    # 第三行第5列：融合结果对比
    axes[2, 4].text(0.1, 0.9, '融合效果预期:', fontweight='bold', fontsize=12, transform=axes[2, 4].transAxes)
    
    # 计算简单的融合结果
    for i, (name, strategy) in enumerate(strategies.items()):
        fused = strategy['w1'] * rgb_feat + strategy['w2'] * sar_feat
        mean_val = np.mean(fused)
        std_val = np.std(fused)
        axes[2, 4].text(0.1, 0.8-i*0.15, f'{name}: μ={mean_val:.3f}, σ={std_val:.3f}', 
                       fontsize=9, transform=axes[2, 4].transAxes)
    
    axes[2, 4].axis('off')
    
    plt.tight_layout()
    plt.savefig('fusion_strategies_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return strategies

def analyze_fusion_effectiveness():
    """分析融合策略的有效性"""
    print("=" * 60)
    print("双模态特征融合策略分析")
    print("=" * 60)
    
    print("\n🤔 原始策略的问题:")
    print("   local_w1 = w1 * similarity")
    print("   local_w2 = w2 * (1 - similarity)")
    print("\n   问题分析:")
    print("   • 在相似区域只用一个模态 → 浪费冗余但有用的信息")
    print("   • 在差异区域只用另一个模态 → 没有充分利用互补性")
    print("   • 权重分配过于极端，缺乏平衡")
    
    print("\n✅ 改进策略的优势:")
    print("\n1. 互补增强策略:")
    print("   • 在差异区域增强两个模态的权重")
    print("   • 充分利用模态间的互补信息")
    print("   • 避免信息丢失")
    
    print("\n2. 选择性互补策略:")
    print("   • 主要关注差异区域")
    print("   • 在相似区域保留少量信息")
    print("   • 平衡效率和效果")
    
    print("\n3. 交叉增强策略:")
    print("   • RGB在差异区域增强（利用其细节优势）")
    print("   • SAR在相似区域增强（利用其稳定性）")
    print("   • 发挥各模态的特长")
    
    print("\n🎯 推荐使用:")
    print("   建议使用'互补增强'或'交叉增强'策略")
    print("   这样更有利于双模态特征交互和信息融合")

if __name__ == '__main__':
    print("开始可视化特征融合策略...")
    
    try:
        strategies = visualize_fusion_strategies()
        analyze_fusion_effectiveness()
        
        print(f"\n✅ 可视化完成！图片已保存为 'fusion_strategies_comparison.png'")
        print("   请查看图片了解不同策略的权重分布差异")
        
    except Exception as e:
        print(f"❌ 可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
