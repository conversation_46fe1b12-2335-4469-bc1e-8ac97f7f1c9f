{"version": "2.0.0", "tasks": [{"label": "Activate Virtual Environment", "type": "shell", "command": "D:\\pycharm exercise\\CMX_main\\.venv\\Scripts\\Activate.ps1", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Python with Virtual Environment", "type": "shell", "command": "D:\\pycharm exercise\\CMX_main\\.venv\\Scripts\\python.exe", "args": ["${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}