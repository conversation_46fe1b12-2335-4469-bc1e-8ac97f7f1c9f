import os.path as osp
import os
import sys
import time
import argparse
from tqdm import tqdm
import multiprocessing

import torch
import torch.nn as nn
import torch.distributed as dist
import torch.backends.cudnn as cudnn
from torch.nn.parallel import DistributedDataParallel

from config import config
from dataloader.dataloader import get_train_loader
from models.builder import EncoderDecoder as segmodel
from dataloader.RGBXDataset import RGBXDataset
from utils.init_func import init_weight, group_weight
from utils.lr_policy import WarmUpPolyLR
from utils.loss_manager import LossManager
from engine.engine import Engine
from engine.logger import get_logger
from utils.pyt_utils import all_reduce_tensor, ensure_dir
from utils.validation import ValidationHelper

from tensorboardX import SummaryWriter

# 定义主函数
def main():
    parser = argparse.ArgumentParser()
    logger = get_logger()

    # 单GPU环境下不需要设置MASTER_PORT
    # os.environ['MASTER_PORT'] = '169710'

    with Engine(custom_parser=parser) as engine:
        args = parser.parse_args()

        cudnn.benchmark = True
        seed = config.seed
        if engine.distributed:
            seed = engine.local_rank
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)

        # data loader
        train_loader, train_sampler = get_train_loader(engine, RGBXDataset)

        # 单GPU环境下直接创建tensorboard目录
        tb_dir = config.tb_dir + '/{}'.format(time.strftime("%b%d_%d-%H-%M", time.localtime()))
        generate_tb_dir = config.tb_dir + '/tb'
        tb = SummaryWriter(log_dir=tb_dir)
        engine.link_tb(tb_dir, generate_tb_dir)

        # config network and criterion
        # 使用新的损失函数管理器
        loss_manager = LossManager(config)
        logger.info(f"使用损失函数类型: {config.loss_type}")
        logger.info(f"损失函数权重: {config.loss_weights}")

        # 为了兼容现有的模型构建，创建一个简单的criterion
        criterion = loss_manager

        # 单GPU环境下使用普通的BatchNorm2d
        BatchNorm2d = nn.BatchNorm2d

        model=segmodel(cfg=config, criterion=criterion, norm_layer=BatchNorm2d)

        # 创建验证助手 - 在模型创建之后初始化
        validation_helper = ValidationHelper(config, model, engine.devices)

        # group weight and config optimizer
        base_lr = config.lr

        params_list = []
        params_list = group_weight(params_list, model, BatchNorm2d, base_lr)

        if config.optimizer == 'AdamW':
            # 使用更稳定的优化器参数
            optimizer = torch.optim.AdamW(
                params_list,
                lr=base_lr,
                betas=(0.9, 0.999),
                eps=1e-8,  # 增加eps值，提高数值稳定性
                weight_decay=config.weight_decay
            )
        elif config.optimizer == 'SGDM':
            optimizer = torch.optim.SGD(
                params_list,
                lr=base_lr,
                momentum=config.momentum,
                dampening=0,  # 添加dampening参数
                weight_decay=config.weight_decay,
                nesterov=True  # 使用Nesterov动量
            )
        else:
            raise NotImplementedError

        # config lr policy
        total_iteration = config.nepochs * config.niters_per_epoch
        lr_policy = WarmUpPolyLR(base_lr, config.lr_power, total_iteration, config.niters_per_epoch * config.warm_up_epoch)

        # 单GPU环境下直接将模型移至GPU
        logger.info('.............single GPU training.............')
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)

        engine.register_state(dataloader=train_loader, model=model,
                              optimizer=optimizer)

        # 检查是否需要从指定的检查点继续训练
        if hasattr(config, 'resume_from') and config.resume_from is not None:
            logger.info(f"从检查点继续训练: {config.resume_from}")
            # 将检查点路径设置到engine中
            engine.continue_state_object = config.resume_from
            engine.restore_checkpoint()
            logger.info(f"成功加载检查点，从第 {engine.state.epoch} 轮继续训练")
        # 兼容原有的命令行参数 -c/--continue
        elif engine.continue_state_object:
            logger.info(f"从命令行指定的检查点继续训练: {engine.continue_state_object}")
            engine.restore_checkpoint()
            logger.info(f"成功加载检查点，从第 {engine.state.epoch} 轮继续训练")
        # 兼容可能存在的旧版本代码
        elif hasattr(config, 'resume_model') and config.resume_model:
            logger.info(f"从旧版本配置的检查点继续训练: {config.resume_model}")
            engine.continue_state_object = config.resume_model
            engine.restore_checkpoint()
            logger.info(f"成功加载检查点，从第 {engine.state.epoch} 轮继续训练")

        optimizer.zero_grad()
        model.train()
        logger.info('begin trainning:')

        for epoch in range(engine.state.epoch, config.nepochs+1):
            # 单GPU环境下不需要设置sampler的epoch
            logger.info(f"开始 Epoch {epoch}...")

            bar_format = '{desc}[{elapsed}<{remaining},{rate_fmt}]'
            pbar = tqdm(range(config.niters_per_epoch), file=sys.stdout,
                        bar_format=bar_format)

            logger.info("创建数据加载器迭代器...")
            dataloader = iter(train_loader)
            logger.info("数据加载器迭代器创建成功")

            sum_loss = 0

            for idx in pbar:
                try:
                    engine.update_iteration(epoch, idx)

                    # logger.info(f"获取第 {idx+1}/{config.niters_per_epoch} 批次数据...")
                    minibatch = next(dataloader)
                    # logger.info(f"成功获取第 {idx+1} 批次数据")

                    imgs = minibatch['data']
                    gts = minibatch['label']
                    modal_xs = minibatch['modal_x']

                    # logger.info(f"数据形状: imgs={imgs.shape}, gts={gts.shape}, modal_xs={modal_xs.shape}")

                    imgs = imgs.cuda(non_blocking=True)
                    gts = gts.cuda(non_blocking=True)
                    modal_xs = modal_xs.cuda(non_blocking=True)
                    # logger.info("数据已移至GPU")

                    # aux_rate参数在模型中未使用，可以忽略
                    # logger.info("开始前向传播...")
                    # 获取模型输出（不传入标签）
                    if hasattr(model, 'aux_head') and model.aux_head:
                        outputs, aux_outputs = model.encode_decode(imgs, modal_xs)
                        # 使用损失管理器计算主要损失
                        loss = loss_manager(outputs, gts, epoch)
                        # 添加辅助损失
                        aux_loss = loss_manager(aux_outputs, gts, epoch)
                        loss += model.aux_rate * aux_loss
                    else:
                        outputs = model.encode_decode(imgs, modal_xs)
                        # 使用损失管理器计算损失
                        loss = loss_manager(outputs, gts, epoch)
                    # logger.info(f"前向传播完成，损失值: {loss.item()}")
                except Exception as e:
                    logger.error(f"处理批次 {idx+1} 时出错: {e}")
                    continue

                # 检查损失值是否为NaN
                if torch.isnan(loss).any() or torch.isinf(loss).any():
                    logger.warning(f"NaN or Inf found in loss: {loss.item()}")
                    # 跳过这个批次
                    continue

                # 单GPU环境下不需要reduce loss
                optimizer.zero_grad()
                loss.backward()

                # 添加梯度裁剪，防止梯度爆炸
                if config.grad_clip > 0:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), config.grad_clip)

                optimizer.step()

                current_idx = (epoch- 1) * config.niters_per_epoch + idx
                lr = lr_policy.get_lr(current_idx)

                for i in range(len(optimizer.param_groups)):
                    optimizer.param_groups[i]['lr'] = lr

                sum_loss += loss.item()
                print_str = 'Epoch {}/{}'.format(epoch, config.nepochs) \
                        + ' Iter {}/{}:'.format(idx + 1, config.niters_per_epoch) \
                        + ' lr=%.4e' % lr \
                        + ' loss=%.4f total_loss=%.4f' % (loss.item(), (sum_loss / (idx + 1)))

                del loss
                pbar.set_description(print_str, refresh=False)

            # 单GPU环境下直接记录tensorboard数据
            tb.add_scalar('train_loss', sum_loss / len(pbar), epoch)

            # 根据配置的频率进行验证
            if epoch % config.eval_frequency == 0 or epoch == config.nepochs:
                logger.info(f"Epoch {epoch}: 开始验证...")
                miou, is_best = validation_helper.validate(epoch, model)
                tb.add_scalar('val_miou', miou, epoch)

                # 只保存最佳模型
                if is_best:
                    logger.info(f"保存最佳模型，mIoU: {miou:.4f}")
                    # 确保检查点目录存在
                    from utils.pyt_utils import ensure_dir
                    ensure_dir(config.checkpoint_dir)
                    logger.info(f"检查点目录: {config.checkpoint_dir}")

                    best_checkpoint_path = osp.join(config.checkpoint_dir, 'best_model.pth')
                    engine.save_checkpoint(best_checkpoint_path)

                    # 创建链接
                    from utils.pyt_utils import link_file
                    last_epoch_checkpoint = osp.join(config.checkpoint_dir, 'epoch-last.pth')
                    link_file(best_checkpoint_path, last_epoch_checkpoint)

                    # 记录最佳模型信息
                    with open(osp.join(config.checkpoint_dir, 'best_model_info.txt'), 'w') as f:
                        f.write(f"Epoch: {epoch}\nmIoU: {miou:.4f}\n")


# 在Windows系统上，需要在主模块中调用main函数
if __name__ == '__main__':
    main()
