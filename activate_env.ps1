# 激活虚拟环境的PowerShell脚本
Write-Host "正在激活虚拟环境..." -ForegroundColor Green

# 设置执行策略（如果需要）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force

# 激活虚拟环境
& "D:\pycharm exercise\CMX_main\.venv\Scripts\Activate.ps1"

Write-Host "虚拟环境已激活！" -ForegroundColor Green
Write-Host "Python路径: " -ForegroundColor Yellow
Get-Command python | Select-Object Source

Write-Host "`n您现在可以运行Python脚本了。" -ForegroundColor Cyan
