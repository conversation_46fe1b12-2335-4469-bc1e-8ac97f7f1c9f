import os
import os.path as osp
import sys
import time
import numpy as np
from easydict import EasyDict as edict
import argparse

C = edict()
config = C
cfg = C

C.seed = 12345

# 在Windows系统上使用cd命令代替pwd
if os.name == 'nt':  # Windows系统
    remoteip = os.popen('cd').read()
else:
    remoteip = os.popen('pwd').read()
C.root_dir = os.path.abspath(os.path.join(os.getcwd(), './'))
C.abs_dir = osp.realpath(".")

# Dataset config
"""Dataset Path"""
C.dataset_name = 'PIE-RGB-SAR'
C.dataset_path = osp.join(C.root_dir, 'datasets', 'PIE-RGB-SAR')
C.rgb_root_folder = osp.join(C.dataset_path, 'RGB')
C.rgb_format = '.tif'
C.gt_root_folder = osp.join(C.dataset_path, 'Label')
C.gt_format = '.tif'
C.gt_transform = True
# True when label 0 is invalid, you can also modify the function _transform_gt in dataloader.RGBXDataset
# True for most dataset valid, Faslse for MFNet(?)
C.x_root_folder = osp.join(C.dataset_path, 'SAR')
C.x_format = '.tif'
C.x_is_single_channel = True # 设置为True，因为SAR图像是单通道的
C.train_source = osp.join(C.dataset_path, "train.txt")
C.eval_source = osp.join(C.dataset_path, "val.txt")
C.is_test = False
C.num_train_imgs = 2432  # 数据集重新划分，训练集：验证集 = 1：1
C.num_eval_imgs = 2433    # 数据集重新划分，训练集：验证集 = 1：1
C.num_classes = 6        # 根据标签分析，有6个类别（0-5）
C.class_names = ['background', 'building', 'farmland', 'forest', 'water', 'road']  # 根据实际类别含义修改

"""Image Config"""
C.background = 255  # 设置为255，与RGBXDataset._gt_transform中的处理一致
C.image_height = 256  # 调整为更合适的尺寸
C.image_width = 256
C.norm_mean = np.array([0.348, 0.370, 0.309])  # 根据PIE-RGB-SAR数据集统计得到
C.norm_std = np.array([0.197, 0.174, 0.171])   # 根据PIE-RGB-SAR数据集统计得到

""" Settings for network, this would be different for each kind of model"""
C.backbone = 'swin_s' # Using Swin Transformer Small model
C.pretrained_model = osp.join(C.root_dir, 'swintrans', 'swin_small_patch4_window7_224_22k.pth')
C.decoder = 'UPernet'  # 改为UPerNet decoder
C.decoder_embed_dim = 512  # UPerNet使用channels参数，但保留此配置以兼容
C.upernet_channels = 512  # UPerNet的通道数配置
C.optimizer = 'AdamW'

"""Train Config"""
C.lr = 1e-5  # 降低学习率，避免梯度爆炸
C.lr_power = 0.9
C.momentum = 0.9
C.weight_decay = 0.01
C.grad_clip = 1.0  # 添加梯度裁剪阈值
C.batch_size = 8  # 减小批处理大小以适应单张显卡
C.nepochs = 300
C.niters_per_epoch = C.num_train_imgs // C.batch_size  + 1
C.num_workers = 0  # Windows系统下设置为0，避免多进程问题
C.train_scale_array = [0.5, 0.75, 1, 1.25, 1.5, 1.75]
C.warm_up_epoch = 20  # 增加预热轮数，使训练更加稳定

C.fix_bias = True
C.bn_eps = 1e-3
C.bn_momentum = 0.1

"""Loss Function Config"""
# 损失函数类型：'ce', 'focal', 'combined', 'adaptive', 'simplified_combined'
C.loss_type = 'ce'

# 损失函数权重配置
C.loss_weights = {
    'ce_weight': 1.0,
    'focal_weight': 0.0,
    'dice_weight': 0.0,
    'lovasz_weight': 0.0,
    'boundary_weight': 0.0,
    'context_weight': 0.0
}

# Focal Loss参数
C.focal_gamma = 2.0
C.focal_alpha = None  # 可以设置为类别权重列表或None

# 标签平滑
C.label_smoothing = 0.0

# 类别权重（可选）
C.class_weights = None  # 例如：[1.0, 2.0, 1.5, 1.0, 1.0, 1.0] 对应6个类别

# 简化组合损失函数的上下文参数
C.context_kernel_size = 3
C.context_dilation = 1

# 自适应损失函数参数
C.adaptive_initial_weights = {'ce_weight': 1.0, 'focal_weight': 0.5, 'dice_weight': 0.3, 'lovasz_weight': 0.2}
C.adaptive_min_weight = 0.1
C.adaptive_max_weight = 2.0
C.adaptive_weight_decay = 0.01
C.adaptive_temperature = 1.0
C.adaptive_weight_lr_ratio = 0.1
C.adaptive_freeze_after_epochs = None

# 日志配置
C.log_loss_components = False  # 是否记录损失组件详细信息

"""Eval Config"""
C.eval_iter = 25
C.eval_stride_rate = 2 / 3
C.eval_scale_array = [1]  # 使用单一尺度进行评估
C.eval_flip = False  # 不使用翻转增强
C.eval_crop_size = [256, 256]  # 调整为与训练图像尺寸一致
C.eval_batch_size = 12  # 验证时的batch size，比训练时更大
C.eval_frequency = 5  # 每5轮进行一次验证

"""Store Config"""
C.checkpoint_start_epoch = 1  # 从第一轮开始保存检查点
C.checkpoint_step = 5  # 每5轮保存一次检查点

"""Resume Config"""
C.resume_from = None  # 从指定检查点继续训练
C.resume_model = None  # 兼容旧版本代码

"""Path Config"""
def add_path(path):
    if path not in sys.path:
        sys.path.insert(0, path)
add_path(osp.join(C.root_dir))

C.log_dir = osp.abspath('log_' + C.dataset_name + '_' + C.backbone)
C.tb_dir = osp.abspath(osp.join(C.log_dir, "tb"))
C.log_dir_link = C.log_dir
C.checkpoint_dir = osp.abspath(osp.join(C.log_dir, "checkpoint"))

exp_time = time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime())
C.log_file = C.log_dir + '/log_' + exp_time + '.log'
C.link_log_file = C.log_file + '/log_last.log'
C.val_log_file = C.log_dir + '/val_' + exp_time + '.log'
C.link_val_log_file = C.log_dir + '/val_last.log'

if __name__ == '__main__':
    print(config.nepochs)
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '-tb', '--tensorboard', default=False, action='store_true')
    parser.add_argument(
        '-r', '--resume', default='', type=str,
        help='从指定的检查点继续训练，例如: ./log_PIE-RGB-SAR_swin_s/checkpoint/best_model.pth')
    parser.add_argument(
        '--resume_model', default=None, type=str,
        help='兼容旧版本代码，从指定的检查点继续训练')
    args = parser.parse_args()

    # 如果命令行指定了resume参数，则更新配置
    if args.resume:
        C.resume_from = args.resume
    # 兼容旧版本代码
    if args.resume_model:
        C.resume_model = args.resume_model